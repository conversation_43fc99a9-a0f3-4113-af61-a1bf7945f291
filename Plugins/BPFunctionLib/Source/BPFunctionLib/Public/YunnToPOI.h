// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
//#include "CesiumGeoreference.h"
#include "GameFramework/Actor.h"

#if WITH_EDITOR
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"
#endif

#include "YunnToPOI.generated.h"


USTRUCT(BlueprintType)
struct FPOIAttributes
{
	GENERATED_USTRUCT_BODY()


    //--------------------通用标签样式------------------------//
	//POI id
	UPROPERTY(BlueprintReadWrite)
	int32 PA_Id;

	//POI位置 
	UPROPERTY(BlueprintReadWrite)
	FVector PA_Location;

	//是否使用widget  默认true
	UPROPERTY(BlueprintReadWrite)
	bool PA_UsedWidget;

	//POI 类型    0-4   0-横向标签  1-竖向标签   2-GIF标签   3-楼栋标签  默认0  4-拉进显示文字标签
	UPROPERTY(BlueprintReadWrite)
	uint8 PA_WidgetStyle;

	//点击标签瞬切
	UPROPERTY(BlueprintReadWrite)
    bool PA_Teleportation;
	
	//是否显示标签小图标    默认true
	UPROPERTY(BlueprintReadWrite)
	bool PA_ShowHeaderImage;

	//标签小图标   url
	UPROPERTY(BlueprintReadWrite)
	FString PA_HeaderImage;

	//背景图前段    url
	UPROPERTY(BlueprintReadWrite)
	FString PA_BackgroundImage1;

	//背景图中段     url
	UPROPERTY(BlueprintReadWrite)
	FString PA_BackgroundImage2;

	//背景图后段     url
	UPROPERTY(BlueprintReadWrite)
	FString PA_BackgroundImage3;

	//POI内容 
	UPROPERTY(BlueprintReadWrite)
	FString PA_Name;

	//文字样式     默认0 暂时只有一种sourcehansanssc-bold_Font
	UPROPERTY(BlueprintReadWrite)
	uint8 PA_FontStyle;

	//文字颜色     RGBA (R=1.000000,G=0.300000,B=0.340000,A=0.560000)
	UPROPERTY(BlueprintReadWrite)
	FLinearColor PA_FontColor;

	//文字轮廓颜色
	UPROPERTY(BlueprintReadWrite)
	FLinearColor PA_OutLineFont;

	//文字放大缩小    默认2
	UPROPERTY(BlueprintReadWrite)
	float  PA_FontEnlargement;


	//是否可以点击  默认false
	UPROPERTY(BlueprintReadWrite)
	bool PA_CanClick;

	//点击图片     url
	UPROPERTY(BlueprintReadWrite)
	FString PA_ClickImage;


	//POI大小缩放   默认1.5
	UPROPERTY(BlueprintReadWrite)
	float PA_WidgetBrushSizeNum;

	//标签小图标大小缩放    默认1
	UPROPERTY(BlueprintReadWrite)
	float PA_HeaderImageSizeNum;

	//点击标签缩放     默认0.7
	UPROPERTY(BlueprintReadWrite)
	float PA_ClickImageSizeNum;

	//点击按钮上下偏移
	UPROPERTY(BlueprintReadWrite)
    float PA_ClickImageOffsetY;

	//点击按钮左右偏移
	UPROPERTY(BlueprintReadWrite)
	float PA_ClickImageOffsetX;
	
	//--------------------标签线和动画样式(通用)------------------------//

	//标签线图片    url
	UPROPERTY(BlueprintReadWrite)
	FString PA_LineImage;

	//标签线长度   默认120
	UPROPERTY(BlueprintReadWrite)
	float PA_LineLength;

	//标签线宽度     默认2
	UPROPERTY(BlueprintReadWrite)
	float PA_LineWidth;

	//标签线上的点图片    url
	UPROPERTY(BlueprintReadWrite)
	FString PA_LinePointImage;

	//标签线上的点图片大小     默认20
	UPROPERTY(BlueprintReadWrite)
    float PA_LinePointImageSizeNum;

	//POI 动画类型(剖面样式)  默认是1   0-6   0-None  1-OpenAlpha  2-OpenScale   3-OpenLeftToRight  4-OpenRightToLeft  5-OpenTopToDown  6-OpenDownToTop
	UPROPERTY(BlueprintReadWrite)
	uint8 PA_OpenType;

	//POI 锚点  默认0，0
	UPROPERTY(BlueprintReadWrite)
	FVector2D PA_InPoivt;

	//动画时间  默认1
	UPROPERTY(BlueprintReadWrite)
	float PA_AnimTime;


	//--------------------横向标签设置------------------------//

	//横向标签镜像  默认false
	UPROPERTY(BlueprintReadWrite)
	bool PA_HorizontalMirror;

	//标签线旋转  范围 -85 ~ 85  默认0
	UPROPERTY(BlueprintReadWrite)
	float PA_HorizontalLineRotation;

	//标签线位置  范围 0 ~ 1   0左 右1  默认0.5
	UPROPERTY(BlueprintReadWrite)
	float PA_HorizontalLinePosition;

	//前段留白   0~2  默认0.9
	UPROPERTY(BlueprintReadWrite)
	float PA_HorizontalLineLeftBlank;

	//尾段留白   0~2  默认0.35
	UPROPERTY(BlueprintReadWrite)
	float PA_HorizontalLineRightBlank;

	//文字距离小图标距离  0~1  0.253
	UPROPERTY(BlueprintReadWrite)
	float PA_HeaderImageTextDistance;

	//标签距离标签线距离  默认 3
	UPROPERTY(BlueprintReadWrite)
    float PA_LineWidgetDistance;

	//文字上下间距调整   正数向上   默认5
	UPROPERTY(BlueprintReadWrite)
	float PA_TextBottomDistance;

	//--------------------竖向标签设置------------------------//
	//竖向标签线距离  默认-19
	UPROPERTY(BlueprintReadWrite)
	float  PA_VerticalLineWidgetDistance;

	//开启竖向标签镜像 默认 false
    UPROPERTY(BlueprintReadWrite)
	bool PA_VerticalMirror;

	//竖向标签顶部留白  0~1 默认0.77
    UPROPERTY(BlueprintReadWrite)
	float PA_VerticalLineTopBlank;

    //竖向标签底部留白   0~1 默认0.5
    UPROPERTY(BlueprintReadWrite)
	float PA_VerticalLineBottomBlank;

    //竖向标签线距离顶部距离    0~1 默认0
	UPROPERTY(BlueprintReadWrite)
    float PA_VerticalLineDistanceTop;

	//竖向标签文字左右偏移
	UPROPERTY(BlueprintReadWrite)
	float PA_VerticalTextOffsetX;

	//--------------------标签tag------------------------//
	//POITag 
	UPROPERTY(BlueprintReadWrite)
	TArray<FString> PA_Tags;

	// 默认构造函数
	FPOIAttributes()
	{
		// 使用你在注释中提供的默认值进行初始化
		PA_Id = 0;
		PA_Location = FVector::ZeroVector;
		PA_UsedWidget = true;
		PA_WidgetStyle = 0;
		PA_Teleportation=false;
		PA_ShowHeaderImage = true;
		PA_HeaderImage = TEXT("");
		PA_BackgroundImage1 = TEXT("");
		PA_BackgroundImage2 = TEXT("");
		PA_BackgroundImage3 = TEXT("");
		PA_Name = TEXT("");
		PA_FontStyle = 0;
		PA_FontColor = FLinearColor(1.0f, 0.3f, 0.34f, 0.56f);
		PA_FontEnlargement = 2.0f;
		PA_CanClick = false;
		PA_ClickImage = TEXT("");
		PA_WidgetBrushSizeNum = 1.5f;
		PA_HeaderImageSizeNum = 1.0f;
		PA_ClickImageSizeNum = 0.7f;
		PA_LineImage = TEXT("");
		PA_LineLength = 120.0f;
		PA_LineWidth = 2.0f;
		PA_LinePointImage = TEXT("");
		PA_LinePointImageSizeNum = 20.0f;
		PA_OpenType = 1;
		PA_InPoivt = FVector2D(0, 0);
		PA_AnimTime = 1.0f;
		PA_HorizontalMirror = false;
		PA_HorizontalLineRotation = 0.0f;
		PA_HorizontalLinePosition = 0.5f;
		PA_HorizontalLineLeftBlank = 0.9f;
		PA_HorizontalLineRightBlank = 0.35f;
		PA_HeaderImageTextDistance = 0.253f;
		PA_LineWidgetDistance = 3.0f;
		PA_TextBottomDistance = 5.0f;
		PA_VerticalLineWidgetDistance = -19.0f;
		PA_VerticalMirror = false;
		PA_VerticalLineTopBlank = 0.77f;
		PA_VerticalLineBottomBlank = 0.5f;
		PA_VerticalLineDistanceTop = 0.0f;
	    PA_VerticalTextOffsetX = 0.0f;
        PA_ClickImageOffsetY = 0.0f;
		PA_ClickImageOffsetX = 0.0f;
		
		PA_Tags.Empty();
	}

};

// 委托声明：异步加载图片成功时回调
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTextureLoaded, UTexture2D*, LoadedTexture, FString, ImageUrl);


UCLASS()
class BPFUNCTIONLIB_API AYunnToPOI : public AActor
{
	GENERATED_BODY()
	
public:	
	// Sets default values for this actor's properties
	AYunnToPOI();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;
	virtual void BeginDestroy() override;
public:	
	// Called every frame
	virtual void Tick(float DeltaTime) override;


	//解析POI数据 转换成键值对
	UFUNCTION(BlueprintCallable, Category = "YunnToPOI")
	void ConvertJsonToPOIAttributes(TMap<FString, FPOIAttributes>& OutAmenitiesMap,FString POIJson);

	//自定义高度
	UPROPERTY(BlueprintReadWrite, Category = "YunnToPOI")
	float UnrealHeight;

	/*//cesium
	UPROPERTY(BlueprintReadWrite, Category = "YunnToPOI")
	ACesiumGeoreference* CesiumGeoreference;*/


public:
	UPROPERTY(BlueprintAssignable, Category = "YunnToPOI")
	FOnTextureLoaded OnTextureLoaded;
	// 输入URL并返回 UTexture2D
		UFUNCTION(BlueprintCallable, Category = "YunnToPOI")
	void LoadTextureFromURL(const FString & ImagePathOrURL);

private:
	// 图片下载并转换为 UTexture2D
	UTexture2D* LoadTextureFromBytes(const TArray<uint8>& ImageData);






	//修改本地poi位置
public:
	UFUNCTION(BlueprintCallable, Category = "YunnToPOI")
	bool SetLocalPOILocation(const FString& JsonFilePath, int32 ItemId, const FString& NewLocationX, const FString& NewLocationY, const FString& NewLocationZ,
		float NewWidgetBrushSizeNum = -1.0f, float NewLineLength = -1.0f, float NewAnimTime = -1.0f, float NewLineRotation = -1.0f);
private:
	// 位置修改的JSON函数
	 bool ModifyLocationInJsonArray(TArray<TSharedPtr<FJsonValue>>& JsonArray, int32 TargetId, const FString& NewLocationX, const FString& NewLocationY, const FString& NewLocationZ,
		float NewWidgetBrushSizeNum, float NewLineLength, float NewAnimTime, float NewLineRotation, bool& bFound);

public:
	// 编辑器通知系统
	UFUNCTION(BlueprintCallable, Category = "YunnToPOI")
	void ShowEditorNotification(const FString& Message);

	UFUNCTION(BlueprintCallable, Category = "YunnToPOI")
	void HideEditorNotification();

private:
	// 静态变量用于跟踪当前通知
	static TSharedPtr<SNotificationItem> CurrentNotificationItem;

};
