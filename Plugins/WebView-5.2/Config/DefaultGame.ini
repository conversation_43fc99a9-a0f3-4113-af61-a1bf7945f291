
; ���ݲ�ͬģʽѡ�������ļ�
; �༭���� [��Ŀ��]\Config\DefaultGame.ini 
; �������� Saved\Config\WindowsEditor\Game.ini 
; ���а��� C:\Users\<USER>\AppData\Local\[��Ŀ��]\Saved\Config\Windows\Game.ini 

[WebView]
keyboard_f5=true           ; F5��ˢ��ҳ��,Ĭ��true
keyboard_ctrl_f5=true      ; Ctrl+F5 ǿ�������������Ⱦ����,Ĭ��true
keyboard_ctrl_f12=true     ; Ctrl+F12 �򿪿���̨,Ĭ��true
keyboard_ctrl_left=true    ; Ctrl+<- ҳ�����,Ĭ��true
keyboard_ctrl_right=true   ; Ctrl+-> ҳ��ǰ��,Ĭ��true
split_frame=0              ; JS���͸�UE��Ϣÿ֡����������Ĭ��0����֡
ctrl_scale=true			   ; Ctrl+������ ����ҳ��,Ĭ��true
touch=true                 ; ������ģʽ�������͸����,Ĭ��false
multi_adapter=true         ; �������Կ�������Ⱦ,Ĭ��true
ue_gpu_cef=true            ; ueֱͨCEFͨ��,Ĭ��true
cef_gpu_ue=true            ; cefֱͨUEͨ��,Ĭ��true
same_direction=true        ; ͬ��ͬ��,Ĭ��false
single_gpu=true            ; ���Կ���Ⱦ,Ĭ�ϸ����Կ��Լ�ѡ��,Ĭ�ϸ���ϵͳѡ��
offgpu=true                ; ʹ��CPU��Ⱦ��ҳ,Ĭ��false
gpuid=0                    ; ʹ�õ�1�������Կ�,Ĭ��-1,�����ӡ�����Կ���Ϣ,-2���������ѡ���Կ�
system_dpi=true            ; ʹ��ϵͳĬ��dpi������ҳ,Ĭ��true
physical_width=3840        ; ��ʾ����߷ֱ��ʿ��,Ĭ��3840
force_develop=false        ; ǿ�ƴ��ڿ����У������²������ʱ�̼����Ȩ��Ч�ԡ�Ĭ�Ϲر�
filter_cef_log=true        ; UE��־�й��˵�CEF��־
clear_log=true             ; �Զ������������־��Ĭ��false
clear_cache=true           ; �Զ�������������棬Ĭ��false,���ֶ�ɾ��Saved\webcorecache 
local_domain=webapp        ; ���ñ������������ĿContent�ĸ�·��,Ĭ�ϲ�����.local://sample1.html
interface_name=web         ; ��ҳ�˽�����Ϣ�Ķ�������,��������ָ��. Ĭ��Ϊ interface. �÷�Ϊ:ue.interface.jsfunc=function(){}
create_time=60000          ; ����������ȴ�ʱ��,Ĭ��60000ms
hide_license=true          ; �ر���־����Ȩ��Ϣ,Ĭ��false
web_drag=false             ; �ر���ҳ��ק,Ĭ��true
web_debug=false            ; �ر�web���Կ���̨,Ĭ��true
high_adapter=true          ; ʹ�ø������Կ�,Ĭ��true
;adapter_luid=0,123123      ; ָ���Կ���Ⱦ, Ĭ�ϲ�����; chrome://gpu �� Version Information �б��е� GPU0 GPU1 GPU2 ... ��Ӧ�� LUID ֵ
machine_code=false         ; ǿ�����ɻ�����,Ĭ��false
certificate_verify=false   ; ���httpsЭ��ssl֤����,Ĭ��false�����
blend_color=R=0 G=0 B=0 A=0  ; ��Ⱦɫ�ʻ��,���Ըı���ҳ��ɫ.�°汾Ĭ��0,�ϰ汾 R=0 G=40 B=100 A=0
frame_rate=20              ; ������ҳ��Ⱦ֡�ʣ�Ĭ��30�����༭��������Ϊ0ʱ����������Ч��
force_wmi=true             ; ����wmic��ȡӲ����Ϣ��Ĭ�Ͽ�����
time_unlock=               ; �ָ�ϵͳʱ��ʱ�õ�key,Keyֻ�е�����Ч��
show_check_time=false      ; ��ʾ���ʱ����Ϣ.Ĭ��false
protocol_wakeup=dingtalk,imos,svn ; Э�黽��
reserve_ue4=true           ; �����ϰ汾����ҳ��ʹ��ue4����
cef_event_loop=true        ; ʹ��CEF�Դ���Ϣѭ�����༭����Ĭ�Ͽ��������״̬Ĭ�Ϲرա�
international_keyboards=true; ʹ�ù��ʼ���
cef_args=                  ; ���cef�ں���������

; cef������������
; enable-webgl : ������ҳWebGL��Ⱦ
; disable-web-security : ��ҳ���������� UE���ܵ��� iframe ��Ƕҳ�溯��ʱ��ӡ�
; unsafely-treat-insecure-origin-as-secure : ����ȫ�ؽ�����ȫ����Դ��Ϊ��ȫ�ġ�����Ϊ������ַ���磺������ҳʹ����˷�
; autoplay-policy=no-user-gesture-required : ������Ƶ�Զ�����
; mute-audio : ����ƵĬ�Ͽ�������,��Ҫʹ���������
; disable-gpu-shader-disk-cache : ����GPU��Ⱦ����
; disable-cache=true : ������ҳ��Ⱦ����
; proxy-server : ����URL  http=proxy.example.com:8080;https=proxy.example.com:8080
; max-connections-per-host : ���������������
; allow-running-insecure-content : ���� HTTPS ҳ����� HTTP ����
; max-web-media-player-count : ����ÿ֡�������� WebMediaPlayers ����
; auto-accept-camera-and-microphone-capture : �ƹ���ʾ�û���ȡ��������ͷ����˷�Ȩ�޵ĶԻ���


; cef_args ��������:
; cef_args=unsafely-treat-insecure-origin-as-secure=http://www.baidu.com autoplay-policy=no-user-gesture-required
; 
; 
; 